/*
 * Copyright (c) 2024. 康成投资（中国）有限公司
 * http://www.rt-mart.com
 * 版权归本公司所有，不得私自使用、拷贝、修改、删除，否则视为侵权
 */

package lib.component.tag;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.RectF;
import android.graphics.Shader;
import android.text.style.ReplacementSpan;

import androidx.annotation.NonNull;

import lib.core.utils.ExCommonUtil;

/**
 * FillBgSpan
 * <p>
 * 纯色背景标签
 * <p>
 * Modified by ye.xue on 2020/03/05
 */
public class FillBgSpan extends ReplacementSpanWithArrow {

    private final Context context;
    private final TagBean data;

    private final float textMargin;
    private final float tagMargin;

    private final Paint tagPaint;
    private final Paint framePaint;
    private final RectF frameRectF;
    private float frameWidth;
    private float frameHeight;

    private final int bgColor;
    private final int bgColorEnd;

    /**
     * 构造方法
     *
     * @param c    上下文
     * @param bean TagBean
     */
    public FillBgSpan(Context c, TagBean bean) {
        this.context = c.getApplicationContext();
        this.data = bean;
        if (this.data.appearance == null) {
            this.data.appearance = TagAppearance.DEFAULT;
        }
        this.textMargin = TagUtils.dpToPx(context, this.data.appearance.textMarginDp);
        this.tagMargin = TagUtils.dpToPx(context, this.data.appearance.tagMarginDp);
        //背景色
        this.bgColor = TagAppearance.safeParseColor(data.bgColor, Color.WHITE);
        //终止背景色
        this.bgColorEnd = TagAppearance.getBgEndColor(data.bgColorEnd);
        framePaint = new Paint();
        framePaint.setStyle(Paint.Style.FILL);
        framePaint.setAntiAlias(true);
        framePaint.setDither(true);
        tagPaint = new Paint();
        //标签文字颜色
        tagPaint.setColor(TagAppearance.safeParseColor(data.textColor, Color.BLACK));
        tagPaint.setTextAlign(Paint.Align.CENTER);
        tagPaint.setAntiAlias(true);
        tagPaint.setDither(true);
        tagPaint.setFakeBoldText(data.appearance.tagTextBold);
        if (bean.isClick) {
            this.StrokeArrowSpan(context, data);
        }
        frameRectF = new RectF();
    }

    @Override
    public int getSize(@NonNull Paint paint, CharSequence text, int start, int end, Paint.FontMetricsInt fm) {
        //行高校正
        TagUtils.checkLineFM(paint, fm);
        //设置标签内文本大小
        float realTagTextSize = data.appearance.getTagTextSize(context, paint, data.useFixedTagHeight);
        tagPaint.setTextSize(realTagTextSize);
        //标签文字与边框间距处理
        float paddingV = data.appearance.getTagTextPaddingV(context, realTagTextSize);
        float paddingH = data.appearance.getTagTextPaddingH(context, realTagTextSize);
        //边框大小确定
        if (end - start == 1) {
            frameWidth = frameHeight = data.appearance.dealForSquareFrame(tagPaint, paddingV);
        } else {
            frameWidth = TagUtils.measureText(tagPaint, text, start, end) + 2 * paddingH;
            Paint.FontMetrics tagFM = tagPaint.getFontMetrics();
            frameHeight = tagFM.descent - tagFM.ascent + 2 * paddingV;
        }

        //若设置了tag的行高则忽律之前的默认设置
        if (data.appearance.tagHeightDp > 0 && data.useFixedTagHeight) {
            frameHeight = TagUtils.dpToPx(context, data.appearance.tagHeightDp);
        }
        if (data.isClick) {
            frameWidth = frameWidth + arrowWidth + arrowMargin;
        }
        int spanWidth = Math.round(frameWidth);

        //多个标签,添加标签间距
        if (!data.isLastTag()) {
            spanWidth += tagMargin;
        }
        //从开始位置显示,最后一个标签,并且后面有文字内容,添加标签与文字间距
        if (data.fromStart && data.hasText && data.isLastTag()) {
            spanWidth += textMargin;
        }

        //从结束位置显示,第一个标签,添加便签与文字间的间距
        if (!data.fromStart && data.isFirstTag()) {
            spanWidth += textMargin;
        }
        return spanWidth;
    }

    @Override
    public void draw(@NonNull Canvas canvas, CharSequence text, int start, int end, float x, int top, int y, int bottom, Paint paint) {
        //TextView内文字高度确定
        Paint.FontMetrics rawFM = paint.getFontMetrics();
        //外部原始边框范围与实际边框差值
        float heightDiff = (rawFM.descent - rawFM.ascent - frameHeight) / 2F;
        float frameLeft;
        if (!data.fromStart && data.isFirstTag()) {
            frameLeft = x + textMargin;
        } else {
            frameLeft = x;
        }
        float frameTop = y + rawFM.ascent + heightDiff;
        float frameBottom = y + rawFM.descent - heightDiff;
        //标签背景范围
        frameRectF.set(frameLeft, frameTop, frameLeft + frameWidth, frameBottom);
        if (bgColorEnd != TagAppearance.COLOR_NONE) {
            //渐变色处理
            framePaint.setShader(new LinearGradient(frameRectF.left,
                    (frameRectF.top + frameRectF.bottom) / 2F,
                    frameRectF.right,
                    (frameRectF.top + frameRectF.bottom) / 2F,
                    new int[]{bgColor, bgColorEnd},
                    null,
                    Shader.TileMode.CLAMP));
        } else {
            //纯色
            framePaint.setColor(bgColor);
        }
        //边框圆角弧度
        float frameCorner = data.appearance.getCornerSize(context, frameHeight);
        //绘制边框
        canvas.drawRoundRect(frameRectF, frameCorner, frameCorner, framePaint);
        if (data.isClick) {
            //绘制箭头
            this.drawArrow(canvas, frameRectF);
        }
        //绘制文字
        canvas.drawText(text, start, end,
                (2F * frameLeft + frameWidth - arrowWidth - arrowMargin) / 2F,
                TagUtils.adjustBaseLine(y, rawFM, tagPaint.getFontMetrics()),
                tagPaint);
    }
}
