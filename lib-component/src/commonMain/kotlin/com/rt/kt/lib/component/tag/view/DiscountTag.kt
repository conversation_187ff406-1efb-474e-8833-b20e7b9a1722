package com.rt.kt.lib.component.tag.view

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.rt.kt.lib.component.tag.TagBean
import com.rt.kt.lib.component.tag.TagType
import com.rt.kt.lib.component.tag.TagUtils

/**
 * @ClassName: DiscountTag
 * @Description:折省标签组件（折省标签）
 * 特殊的分段显示标签，通常用于显示折扣信息
 * 例如："折"和"省"分别显示在不同的背景区域
 * @Date: 2025/6/4
 */

@Composable
fun DiscountTag(
    tagBean: TagBean,
    onClick: ((TagBean) -> Unit)? = null,
    modifier: Modifier = Modifier,
    preCalculatedHeight: Float? = null // 预计算的高度（dp值）
) {
    require(tagBean.type == TagType.DISCOUNT) {
        "DiscountTag only supports DISCOUNT type"
    }

    val appearance = tagBean.appearance
    val text = tagBean.text

    // 如果文字长度小于2，则按普通标签处理
    if (text.length < 2) {
        FillTag(
            tagBean = tagBean.copy(type = TagType.FILL),
            onClick = onClick,
            modifier = modifier
        )
        return
    }

    // 分割文字：第一个字符和剩余字符
    val firstChar = text.substring(0, 1)
    val remainingText = text.substring(1)

    // 点击修饰符
    val clickModifier = if (tagBean.isClickable && onClick != null) {
        Modifier.clickable { onClick(tagBean) }
    } else {
        Modifier
    }

    // 使用预计算的高度或计算新的高度  否则使用TagUtils中统一的高度计算逻辑
    val iconHeight = preCalculatedHeight?.dp ?: TagUtils.calculateTagHeight(tagBean)

    // 🎯 使用缓存的文字大小计算，避免重复计算
    val realTagTextSize = TagUtils.getTagTextSizeCached(tagBean)

    Row(
        modifier = modifier
            .then(clickModifier)
            .clip(appearance.shape),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        // 第一部分（通常是"折"）
        Box(
            modifier = Modifier
                .height(iconHeight)
                .background(
                    color = tagBean.backgroundColor,
                    shape = RoundedCornerShape(
                        topStart = appearance.cornerRadius,
                        bottomStart = appearance.cornerRadius,
                        topEnd = 0.dp,
                        bottomEnd = 0.dp
                    )
                )
                .border(
                    width = appearance.borderWidth,
                    color = tagBean.borderColor,
                    shape = RoundedCornerShape(
                        topStart = appearance.cornerRadius,
                        bottomStart = appearance.cornerRadius,
                        topEnd = 0.dp,
                        bottomEnd = 0.dp
                    )
                )
                .padding(
                    horizontal = appearance.horizontalPadding,
                    vertical = appearance.verticalPadding
                ),
            contentAlignment = Alignment.Center
        ) {
            TagText(
                text = firstChar,
                fontSize = realTagTextSize.sp,
                color = tagBean.textColor,
                fontWeight = appearance.fontWeight,
                modifier = Modifier.background(Color.Green)

            )
        }

        // 第二部分（通常是"省"或其他内容）
        Box(
            modifier = Modifier
                .height(iconHeight)
                .background(
                    color = tagBean.backgroundEndColor ?: tagBean.backgroundColor,
                    shape = RoundedCornerShape(
                        topStart = 0.dp,
                        bottomStart = 0.dp,
                        topEnd = appearance.cornerRadius,
                        bottomEnd = appearance.cornerRadius
                    )
                )
                .border(
                    width = appearance.borderWidth,
                    color = tagBean.borderColor,
                    shape = RoundedCornerShape(
                        topStart = 0.dp,
                        bottomStart = 0.dp,
                        topEnd = appearance.cornerRadius,
                        bottomEnd = appearance.cornerRadius
                    )
                )
                .padding(
                    horizontal = appearance.horizontalPadding,
                    vertical = appearance.verticalPadding
                ),
            contentAlignment = Alignment.Center
        ) {
            TagText(
                text = remainingText,
                fontSize = realTagTextSize.sp,
                color = tagBean.textColor,
                fontWeight = appearance.fontWeight,
            )
        }

        // 如需展示箭头
        if (tagBean.shouldShowArrow()) {
            Spacer(modifier = Modifier.width(appearance.arrowSpacing))
            Icon(
                imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                contentDescription = "arrow",
                tint = tagBean.textColor,
                modifier = Modifier.size(appearance.arrowWidth)
            )
        }
    }
}
