/*
 * Copyright (c) 2024. 康成投资（中国）有限公司
 * http://www.rt-mart.com
 * 版权归本公司所有，不得私自使用、拷贝、修改、删除，否则视为侵权
 */

package lib.component.tag;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Rect;
import android.graphics.RectF;
import android.text.style.ReplacementSpan;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;

import lib.core.utils.ExCommonUtil;

/**
 * 积分标
 *
 * <AUTHOR>
 * @Date 2024/1/6
 */
public class JFSpan extends ReplacementSpanWithArrow {

    private final Context context;
    private final TagBean data;

    private final float textMargin;
    private final float tagMargin;

    private final Paint tagPaint;
    private final Paint framePaint;
    private final RectF frameRectF;
    //积分图片框
    private final RectF frame1RectF;
    //积分内容框
    private final RectF frame2RectF;

    //总的宽度
    private float frameWidth;
    private float frameHeight;

    private final int bgColor;
    private final int bgColorEnd;
    private final int borderColor;

    //积分图片 边框的宽度
    private float frame1Width;
    //积分内容边框宽度
    private float frame2Width;

    private final Path mPath;

    private final @DrawableRes int jfResId;

    private final float frontImgRate;

    /**
     * 构造方法
     *
     * @param c    上下文
     * @param bean TagBean
     */
    public JFSpan(Context c, TagBean bean) {
        this.context = c.getApplicationContext();
        this.data = bean;
        if (this.data.appearance == null) {
            this.data.appearance = TagAppearance.DEFAULT;
        }
        this.jfResId = this.data.appearance.jfResId;
        this.textMargin = TagUtils.dpToPx(context, this.data.appearance.textMarginDp);
        this.tagMargin = TagUtils.dpToPx(context, this.data.appearance.tagMarginDp);
        //背景色
        this.bgColor = TagAppearance.safeParseColor(data.bgColor, Color.TRANSPARENT);
        //终止背景色
        this.bgColorEnd = TagAppearance.safeParseColor(data.bgColorEnd, Color.TRANSPARENT);
        //边框颜色
        this.borderColor = TagAppearance.safeParseColor(data.borderColor, Color.TRANSPARENT);

        this.frontImgRate = this.data.appearance.frontImgRate;

        framePaint = new Paint();
        framePaint.setStrokeWidth(TagUtils.dpToPx(context, this.data.appearance.borderSizeDp));
        framePaint.setAntiAlias(true);
        framePaint.setDither(true);
        //标签文字颜色
        tagPaint = new Paint();
        tagPaint.setColor(TagAppearance.safeParseColor(data.textColor, Color.BLACK));
        tagPaint.setTextAlign(Paint.Align.CENTER);
        tagPaint.setAntiAlias(true);
        tagPaint.setDither(true);
        tagPaint.setFakeBoldText(data.appearance.tagTextBold);
        frameRectF = new RectF();
        frame1RectF = new RectF();
        frame2RectF = new RectF();
        if (bean.isClick) {
            this.StrokeArrowSpan(context, data);
        }
        mPath = new Path();
    }

    @Override
    public int getSize(@NonNull Paint paint, CharSequence text, int start, int end, Paint.FontMetricsInt fm) {
        //行高校正
        TagUtils.checkLineFM(paint, fm);
        //设置标签内文本大小
        float realTagTextSize = data.appearance.getTagTextSize(context, paint, data.useFixedTagHeight);
        tagPaint.setTextSize(realTagTextSize);
        //标签文字与边框间距处理
        float paddingV = data.appearance.getTagTextPaddingV(context, realTagTextSize);
        float paddingH = data.appearance.getTagTextPaddingH(context, realTagTextSize);
        //边框大小确定
        Paint.FontMetrics tagFM = tagPaint.getFontMetrics();
        frameHeight = tagFM.descent - tagFM.ascent + 2 * paddingV;

        //若设置了tag的行高则忽律之前的默认设置
        if (data.appearance.tagHeightDp > 0 && data.useFixedTagHeight) {
            frameHeight = TagUtils.dpToPx(context, data.appearance.tagHeightDp);
        }

        frame1Width = frameHeight;
        frame2Width = TagUtils.measureText(tagPaint, text, start, end) + 2 * paddingH;
        frameWidth = frame1Width + frame2Width;
        if (data.isClick) {
            frameWidth = frameWidth + arrowWidth + arrowMargin;
        }

        int spanWidth = Math.round(frameWidth);


        //多个标签,添加标签间距
        if (!data.isLastTag()) {
            spanWidth += tagMargin;
        }
        //从开始位置显示,最后一个标签,并且后面有文字内容,添加标签与文字间距
        if (data.fromStart && data.hasText && data.isLastTag()) {
            spanWidth += textMargin;
        }

        //从结束位置显示,第一个标签,添加便签与文字间的间距
        if (!data.fromStart && data.isFirstTag()) {
            spanWidth += textMargin;
        }
        return spanWidth;
    }

    @Override
    public void draw(@NonNull Canvas canvas, CharSequence text, int start, int end, float x, int top, int y, int bottom, Paint paint) {
        //TextView内文字高度确定
        Paint.FontMetrics rawFM = paint.getFontMetrics();
        //外部原始边框范围与实际边框差值
        float heightDiff = (rawFM.descent - rawFM.ascent - frameHeight) / 2F;
        float frameLeft;
        if (!data.fromStart && data.isFirstTag()) {
            frameLeft = x + textMargin;
        } else {
            frameLeft = x;
        }
        float frameTop = y + rawFM.ascent + heightDiff;
        float frameBottom = y + rawFM.descent - heightDiff;

        //打标框的范围
        float borderSize = TagUtils.dpToPx(context, data.appearance.borderSizeDp);
        float halfBorderSize = borderSize / 2;
        //标签背景范围
        frameRectF.set(frameLeft + halfBorderSize, frameTop + halfBorderSize, frameLeft + frameWidth - halfBorderSize, frameBottom - halfBorderSize);
        //积分标图片背景范围
        frame1RectF.set(frameLeft + halfBorderSize, frameTop + halfBorderSize, frameLeft + frame1Width - halfBorderSize, frameBottom - halfBorderSize);
        //积分标内容范围
        frame2RectF.set(frameLeft + frame1RectF.width() + halfBorderSize, frameTop + halfBorderSize, frameLeft + frameWidth - halfBorderSize, frameBottom - halfBorderSize);

        //边框圆角弧度
        float frameCorner = data.appearance.getCornerSize(context, frameHeight);

        mPath.reset();
        framePaint.setStyle(Paint.Style.FILL_AND_STROKE);
        mPath.addRoundRect(frame1RectF, new float[]{frameCorner, frameCorner, 0F, 0F, 0F, 0F, frameCorner, frameCorner}, Path.Direction.CCW);
        framePaint.setColor(bgColor);
        canvas.drawPath(mPath, framePaint);

        mPath.reset();
        framePaint.setStyle(Paint.Style.FILL_AND_STROKE);
        mPath.addRoundRect(frame2RectF, new float[]{0, 0, frameCorner, frameCorner, frameCorner, frameCorner, 0, 0}, Path.Direction.CCW);
        framePaint.setColor(bgColorEnd);
        canvas.drawPath(mPath, framePaint);

        //绘制边框
        framePaint.setColor(borderColor);
        framePaint.setStyle(Paint.Style.STROKE);
        canvas.drawRoundRect(frameRectF, frameCorner, frameCorner, framePaint);

        //绘制箭头
        if (data.isClick) {
            this.drawArrow(canvas, frameRectF);
        }

        //绘制积分标图片
        //获取图片
        Bitmap bmp = TagUtils.getJFBitmap(context.getResources(), jfResId);
        Rect src = new Rect(0, 0, bmp.getWidth(), bmp.getHeight());
        Rect dst = new Rect((int) (frame1RectF.left + frame1RectF.width() * (1 - frontImgRate) / 2F), (int) (frame1RectF.top + frame1RectF.height() * (1 - frontImgRate) / 2F), (int) (frame1RectF.right - frame1RectF.width() * (1 - frontImgRate) / 2F), (int) (frame1RectF.bottom - frame1RectF.height() * (1 - frontImgRate) / 2F));
        canvas.drawBitmap(bmp, src, dst, tagPaint);

        //绘制积分标文字内容
        canvas.drawText(text, start, end, frame1RectF.left + frame1Width + frame2Width / 2, TagUtils.adjustBaseLine(y, rawFM, tagPaint.getFontMetrics()), tagPaint);

        //绘制中间竖线
        canvas.drawLine(frame1RectF.right, frame1RectF.top, frame1RectF.right, frame1RectF.bottom, framePaint);
    }
}
