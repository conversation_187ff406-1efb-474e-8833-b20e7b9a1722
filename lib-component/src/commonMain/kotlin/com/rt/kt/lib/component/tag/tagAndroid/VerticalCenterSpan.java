/*
 * Copyright (c) 2024. 康成投资（中国）有限公司
 * http://www.rt-mart.com
 * 版权归本公司所有，不得私自使用、拷贝、修改、删除，否则视为侵权
 */

package lib.component.tag;

import android.graphics.Canvas;
import android.graphics.Paint;
import android.text.TextPaint;
import android.text.style.ReplacementSpan;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * 使TextView中不同大小字体垂直居中
 *
 * <AUTHOR>
 * @Date 2023/5/11
 */
public class VerticalCenterSpan extends ReplacementSpan {

    private final float fontSizePx;

    public VerticalCenterSpan(float fontSizePx) {
        this.fontSizePx = fontSizePx;
    }

    @Override
    public int getSize(@NonNull Paint paint, CharSequence text, int start, int end, @Nullable Paint.FontMetricsInt fm) {
        CharSequence content = text.subSequence(start, end);
        Paint p = getTextPaint(paint);
        return (int) p.measureText(content.toString());
    }

    @Override
    public void draw(@NonNull Canvas canvas, CharSequence text, int start, int end, float x, int top, int y, int bottom, @NonNull Paint paint) {
        Paint p = getTextPaint(paint);
        Paint.FontMetrics fontMetricsInt = p.getFontMetrics();
        int offsetY = (int) ((y + fontMetricsInt.ascent + y + fontMetricsInt.descent) / 2 - (top + bottom) / 2);
        //此处重新计算y坐标,使字体居中
        canvas.drawText(text, start, end, x, y - offsetY, p);
    }

    private TextPaint getTextPaint(Paint paint) {
        TextPaint textPaint = new TextPaint(paint);
        textPaint.setTextSize(fontSizePx);
        return textPaint;
    }
}
