package com.rt.kt.lib.component.tag.view

import com.rt.kt.lib.component.tag.ImageLoaderManager
import com.rt.kt.lib.component.tag.TagBean
import com.rt.kt.lib.component.tag.TagType
import com.rt.kt.lib.component.tag.TagUtils
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * @ClassName: PointsTag
 * @Description:积分标签组件
 * 带图标的积分标签，左侧显示图标，右侧显示积分文字
 * @Date: 2025/6/4
 */

@Composable
fun PointsTag(
    tagBean: TagBean,
    onClick: ((TagBean) -> Unit)? = null,
    modifier: Modifier = Modifier,
    preCalculatedHeight: Float? = null // 预计算的高度（dp值）
) {
    require(tagBean.type == TagType.POINTS) {
        "PointsTag only supports POINTS type"
    }

    val appearance = tagBean.appearance

    // 点击修饰符
    val clickModifier = if (tagBean.isClickable && onClick != null) {
        Modifier.clickable { onClick(tagBean) }
    } else {
        Modifier
    }

    Row(
        modifier = modifier
            .then(clickModifier)
            .fillMaxWidth()
            .background(Color.Yellow)
            .clip(appearance.shape),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        // 使用预计算的高度或计算新的高度
        val iconSize = if (preCalculatedHeight != null) {
            preCalculatedHeight.dp
        } else {
            // 图标部分 - 使用统一的高度计算
            val tagHeight = TagUtils.calculateTagHeight(tagBean)
            tagHeight.value.dp
        }
        Box(
            modifier = Modifier
                .size(iconSize)
                .background(
                    color = tagBean.backgroundColor,
                    shape = RoundedCornerShape(
                        topStart = appearance.cornerRadius,
                        bottomStart = appearance.cornerRadius,
                        topEnd = 0.dp,
                        bottomEnd = 0.dp
                    )
                )
                .border(
                    width = appearance.borderWidth,
                    color = tagBean.borderColor,
                    shape = RoundedCornerShape(
                        topStart = appearance.cornerRadius,
                        bottomStart = appearance.cornerRadius,
                        topEnd = 0.dp,
                        bottomEnd = 0.dp
                    )
                ),
            contentAlignment = Alignment.Center
        ) {
            // 图标通过ImageLoaderManager的回调机制处理
            var iconImageBitmap by remember { mutableStateOf<ImageBitmap?>(null) }
            var isLoading by remember { mutableStateOf(true) }
            var hasError by remember { mutableStateOf(false) }

            LaunchedEffect(tagBean.imageUrl) {
                tagBean.imageUrl?.let { url ->
                    isLoading = true
                    hasError = false

                    ImageLoaderManager.loadImageCompose(
                        url = url,
                        onSuccess = { imageBitmap ->
                            iconImageBitmap = imageBitmap
                            isLoading = false
                            hasError = false
                        },
                        onFailure = { failImageBitmap ->
                            iconImageBitmap = failImageBitmap
                            isLoading = false
                            hasError = true
                        }
                    )
                } ?: run {
                    // 没有URL，直接设置为失败状态
                    isLoading = false
                    hasError = true
                }
            }

            when {
                isLoading -> {
                    // 加载中状态 - 显示空白
                    Box(modifier = Modifier.fillMaxSize())
                }
                iconImageBitmap != null -> {
                    // 加载成功 - 显示自定义图标
                    Image(
                        bitmap = iconImageBitmap!!,
                        contentDescription = "Points icon",
                        modifier = Modifier.size(iconSize * appearance.frontImageRate),
                        contentScale = ContentScale.Fit
                    )
                }
                else -> {
                    // 加载失败 - 显示空白
                    Box(modifier = Modifier.fillMaxSize())
                }
            }
        }

        // 分隔线
//        Box(
//            modifier = Modifier
//                .width(appearance.borderWidth)
//                .height(iconSize)
//                .background(tagBean.borderColor)
//        )

        // 文字部分
        Box(
            modifier = Modifier
                .height(iconSize)
                .background(
                    color = tagBean.backgroundEndColor ?: tagBean.backgroundColor,
                    shape = RoundedCornerShape(
                        topStart = 0.dp,
                        bottomStart = 0.dp,
                        topEnd = appearance.cornerRadius,
                        bottomEnd = appearance.cornerRadius
                    )
                )
                .border(
                    width = appearance.borderWidth,
                    color = tagBean.borderColor,
                    shape = RoundedCornerShape(
                        topStart = 0.dp,
                        bottomStart = 0.dp,
                        topEnd = appearance.cornerRadius,
                        bottomEnd = appearance.cornerRadius
                    )
                )
                .padding(
                    horizontal = appearance.horizontalPadding,
                    vertical = appearance.verticalPadding
                ),
            contentAlignment = Alignment.Center
        ) {
            // 使用缓存的文字大小计算，避免重复计算
            val realTagTextSize = TagUtils.getTagTextSizeCached(tagBean)
            TagText(
                text = tagBean.text,
                fontSize = realTagTextSize.sp,
                color = tagBean.textColor,
                fontWeight = appearance.fontWeight
            )
        }

        // 如需展示箭头
        if (tagBean.shouldShowArrow()) {
            Spacer(modifier = Modifier.width(appearance.arrowSpacing))
            Icon(
                imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                contentDescription = "arrow",
                tint = tagBean.textColor,
                modifier = Modifier.size(appearance.arrowWidth)
            )
        }
    }
}
