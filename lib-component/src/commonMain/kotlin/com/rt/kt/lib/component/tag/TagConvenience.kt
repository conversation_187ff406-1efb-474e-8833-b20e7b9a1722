package com.rt.kt.lib.component.tag

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp

/**
 * @ClassName: TagConvenience
 * @Description:
 * @Date: 2025/6/5
 */
/**
 * 标签便捷方法
 *
 * 这些方法是对TagGroup的封装，提供更简洁的API调用方式。
 */

/**
 * 显示矩形标签在文字前面
 *
 * @param tags 标签列表
 * @param content 文字内容
 * @param onTagClick 标签点击回调
 * @param maxLines 最大行数
 * @param overflow 文字溢出处理方式
 */
@Composable
fun ShowRectStart(
    tags: List<TagBean>,
    content: String,
    onTagClick: ((TagBean) -> Unit)? = null,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Clip
) {
    TagGroup(
        tags = tags,
        text = content,
        showTagsAtStart = true,
        onTagClick = onTagClick,
        forceTagHeight = false,
        maxLines = maxLines,
        overflow = overflow
    )
}

/**
 * 显示矩形标签在文字后面
 *
 * @param tags 标签列表
 * @param content 文字内容
 * @param onTagClick 标签点击回调
 * @param maxLines 最大行数
 * @param overflow 文字溢出处理方式
 */
@Composable
fun ShowRectEnd(
    tags: List<TagBean>,
    content: String,
    onTagClick: ((TagBean) -> Unit)? = null,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Clip
) {
    TagGroup(
        tags = tags,
        text = content,
        showTagsAtStart = false,
        onTagClick = onTagClick,
        forceTagHeight = false,
        maxLines = maxLines,
        overflow = overflow
    )
}

/**
 * 显示圆角标签在文字前面
 *
 * @param tags 标签列表
 * @param content 文字内容
 * @param onTagClick 标签点击回调
 * @param maxLines 最大行数
 * @param overflow 文字溢出处理方式
 */
@Composable
fun ShowRoundStart(
    tags: List<TagBean>,
    content: String,
    onTagClick: ((TagBean) -> Unit)? = null,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Clip
) {
    val roundTags = tags.map { tag ->
        tag.copy(appearance = tag.appearance.copy(
            cornerRadius = 12.dp,
            shape = RoundedCornerShape(12.dp)
        ))
    }

    TagGroup(
        tags = roundTags,
        text = content,
        showTagsAtStart = true,
        onTagClick = onTagClick,
        forceTagHeight = false,
        maxLines = maxLines,
        overflow = overflow
    )
}

/**
 * 显示圆角标签在文字后面
 *
 * @param tags 标签列表
 * @param content 文字内容
 * @param onTagClick 标签点击回调
 * @param maxLines 最大行数
 * @param overflow 文字溢出处理方式
 */
@Composable
fun ShowRoundEnd(
    tags: List<TagBean>,
    content: String,
    onTagClick: ((TagBean) -> Unit)? = null,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Clip
) {
    val roundTags = tags.map { tag ->
        tag.copy(appearance = tag.appearance.copy(
            cornerRadius = 12.dp,
            shape = RoundedCornerShape(12.dp)
        ))
    }

    TagGroup(
        tags = roundTags,
        text = content,
        showTagsAtStart = false,
        onTagClick = onTagClick,
        forceTagHeight = false,
        maxLines = maxLines,
        overflow = overflow
    )
}

/**
 * 显示标签（通用方法）
 *
 * @param tags 标签列表
 * @param content 文字内容
 * @param showTagsAtStart 标签是否显示在文字前面
 * @param appearance 默认样式（当标签没有指定样式时使用）
 * @param onTagClick 标签点击回调
 * @param maxLines 最大行数
 * @param overflow 文字溢出处理方式
 * @param forceTagHeight 是否强制标签高度
 */
@Composable
fun ShowTag(
    tags: List<TagBean>,
    content: String,
    showTagsAtStart: Boolean = true,
    appearance: TagAppearance = TagAppearance.Default,
    onTagClick: ((TagBean) -> Unit)? = null,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Clip,
    forceTagHeight: Boolean = false
) {
    // 为没有指定样式的标签设置默认样式
    val processedTags = tags.map { tag ->
        if (tag.appearance == TagAppearance.Default) {
            tag.copy(appearance = appearance)
        } else {
            tag
        }
    }

    TagGroup(
        tags = processedTags,
        text = content,
        showTagsAtStart = showTagsAtStart,
        onTagClick = onTagClick,
        forceTagHeight = forceTagHeight,
        maxLines = maxLines,
        overflow = overflow
    )
}

/**
 * List<TagBean>的扩展函数：显示在文字前面（矩形）
 */
@Composable
fun List<TagBean>.showRectStart(
    content: String,
    onTagClick: ((TagBean) -> Unit)? = null,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Clip
) {
    ShowRectStart(this, content, onTagClick, maxLines, overflow)
}

/**
 * List<TagBean>的扩展函数：显示在文字后面（矩形）
 */
@Composable
fun List<TagBean>.showRectEnd(
    content: String,
    onTagClick: ((TagBean) -> Unit)? = null,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Clip
) {
    ShowRectEnd(this, content, onTagClick, maxLines, overflow)
}

/**
 * List<TagBean>的扩展函数：显示在文字前面（圆角）
 */
@Composable
fun List<TagBean>.showRoundStart(
    content: String,
    onTagClick: ((TagBean) -> Unit)? = null,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Clip
) {
    ShowRoundStart(this, content, onTagClick, maxLines, overflow)
}

/**
 * List<TagBean>的扩展函数：显示在文字后面（圆角）
 */
@Composable
fun List<TagBean>.showRoundEnd(
    content: String,
    onTagClick: ((TagBean) -> Unit)? = null,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Clip
) {
    ShowRoundEnd(this, content, onTagClick, maxLines, overflow)
}
