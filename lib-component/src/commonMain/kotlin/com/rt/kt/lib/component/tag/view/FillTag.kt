package com.rt.kt.lib.component.tag.view

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.rt.kt.lib.component.tag.TagBean
import com.rt.kt.lib.component.tag.TagType
import com.rt.kt.lib.component.tag.TagUtils

/**
 * @ClassName: FillTag
 * @Description: 填充背景标签组件
 * 支持纯色和渐变背景,边框
 * @Date: 2025/6/4
 */

@Composable
fun FillTag(
    tagBean: TagBean,
    onClick: ((TagBean) -> Unit)? = null,
    modifier: Modifier = Modifier,
    preCalculatedHeight: Float? = null, // 预计算的高度（dp值）
    preCalculatedTextSize: Float? = null // 预计算的文字大小（sp值）
) {
    require(tagBean.type == TagType.FILL || tagBean.type == TagType.FILL_AND_STROKE) {
        "FillTag only supports FILL and FILL_AND_STROKE types"
    }

    val appearance = tagBean.appearance

    // 创建背景修饰符
    val backgroundModifier = if (tagBean.hasGradientBackground()) {
        // 渐变背景 - 使用处理后的结束颜色
        val endColor = tagBean.getProcessedBackgroundEndColor() ?: tagBean.backgroundEndColor!!
        val gradient = Brush.horizontalGradient(
            colors = listOf(tagBean.backgroundColor, endColor)
        )
        Modifier.background(gradient, appearance.shape)
    } else {
        // 纯色背景
        Modifier.background(tagBean.backgroundColor, appearance.shape)
    }

    // 边框修饰符（用于FILL_AND_STROKE类型）
    val borderModifier = if (tagBean.type == TagType.FILL_AND_STROKE) {
        Modifier.border(
            width = appearance.borderWidth,
            color = tagBean.borderColor,
            shape = appearance.shape
        )
    } else {
        Modifier
    }

    // 点击修饰符
    val clickModifier = if (tagBean.isClickable && onClick != null) {
        Modifier.clickable { onClick(tagBean) }
    } else {
        Modifier
    }

    // 使用预计算的文字大小或计算新的文字大小，避免重复计算
    val realTagTextSize = preCalculatedTextSize ?: TagUtils.getTagTextSizeCached(tagBean)
    // 使用预计算的高度或计算新的高度  否则使用TagUtils中统一的高度计算逻辑
    val iconHeight = preCalculatedHeight?.dp ?: TagUtils.calculateTagHeight(tagBean, realTagTextSize)

    Row(
        modifier = modifier
            .then(Modifier.height(iconHeight))
            .then(backgroundModifier)
            .then(borderModifier)
            .then(clickModifier)
            .clip(appearance.shape)
            .padding(
                horizontal = appearance.horizontalPadding,
                vertical = appearance.verticalPadding
            ),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        // 标签文字 - 使用专用的TagText组件消除上下间距
        TagText(
            text = tagBean.text,
            fontSize = realTagTextSize.sp,
            color = tagBean.textColor,
            fontWeight = appearance.fontWeight
        )

        // 如需展示箭头
        if (tagBean.shouldShowArrow()) {
            Spacer(modifier = Modifier.width(appearance.arrowSpacing))
            Icon(
                imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                contentDescription = "arrow",
                tint = tagBean.textColor,
                modifier = Modifier.size(appearance.arrowWidth)
            )
        }
    }
}


