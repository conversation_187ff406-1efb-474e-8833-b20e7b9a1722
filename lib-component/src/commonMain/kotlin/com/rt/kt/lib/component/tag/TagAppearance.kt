package com.rt.kt.lib.component.tag

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * @ClassName: TagAppearance
 * @Description:
 * @Date: 2025/6/4
 */
/**
 * 标签样式配置类
 *
 * 这个类定义了标签的所有视觉样式属性，包括文字、边框、圆角、间距等。
 * 对应原生Android版本的TagAppearance类，完全适配Compose使用。
 *
 * 主要功能：
 * 1. 文字样式控制（大小、粗细、颜色等）
 * 2. 形状和边框控制（圆角、边框宽度等）
 * 3. 间距和布局控制（内边距、外边距等）
 * 4. 特殊效果控制（图片比例、箭头样式等）
 *
 * 预定义样式：
 * - Default: 默认样式，适合大多数场景
 * - Round: 圆角样式，更加柔和
 * - Capsule: 胶囊样式，完全圆角
 * - Square: 方形样式，无圆角
 *
 * 使用示例：
 * ```kotlin
 * // 使用预定义样式
 * val appearance = TagAppearance.Round
 *
 * // 自定义样式
 * val customAppearance = TagAppearance(
 *     textSize = 14.sp,
 *     cornerRadius = 8.dp,
 *     horizontalPadding = 12.dp
 * )
 *
 * // 基于预定义样式修改
 * val modifiedAppearance = TagAppearance.Default.withTextSize(16.sp)
 * ```
 */
data class TagAppearance(
    /**
     * 标签文字大小
     */
    val textSize: TextUnit = 12.sp,

    /**
     * 固定标签文字大小（当使用固定高度时）
     */
    val fixedTextSize: TextUnit = 12.sp,

    /**
     * 标签高度（-1表示自适应）
     */
    val tagHeight: Dp = (-1).dp,

    /**
     * 文字是否加粗
     */
    val fontWeight: FontWeight = FontWeight.Normal,

    /**
     * 边框宽度
     */
    val borderWidth: Dp = 0.5.dp,

    /**
     * 圆角大小
     */
    val cornerRadius: Dp = 4.dp,

    /**
     * 标签形状
     */
    val shape: Shape = RoundedCornerShape(cornerRadius),

    /**
     * 文字与标签边缘的水平间距
     */
    val horizontalPadding: Dp = 6.dp,

    /**
     * 文字与标签边缘的垂直间距
     */
    val verticalPadding: Dp = 2.dp,

    /**
     * 标签之间的间距
     */
    val tagSpacing: Dp = 4.dp,

    /**
     * 标签与文字之间的间距
     */
    val textSpacing: Dp = 4.dp,

    /**
     * 图片高度限制比例（相对于文字高度）
     */
    val imageHeightRatio: Float = 0.95f,

    /**
     * 箭头宽度（可点击标签）
     */
    val arrowWidth: Dp = 10.dp,

    /**
     * 箭头与标签内容的间距
     */
    val arrowSpacing: Dp = 2.dp,

    /**
     * 行间距额外值
     */
    val lineSpacingExtra: Dp = 0.dp,

    /**
     * 圆形边框缩放比例
     */
    val circleFrameScale: Float = 1.05f,

    /**
     * 前置图片比例（用于积分标签）
     */
    val frontImageRate: Float = 0.8f,

    /**
     * 默认标签文字大小比例
     */
    val defaultTagTextSizeRate: Float = 0.72f,

    /**
     * 默认圆角比例
     */
    val defaultRadiusRate: Float = 0.2f,

    /**
     * 默认垂直内边距比例
     */
    val defaultPaddingVerticalRate: Float = 0.10f,

    /**
     * 默认水平内边距比例
     */
    val defaultPaddingHorizontalRate: Float = 0.32f,

    /**
     * 积分标签资源ID（Android平台使用）
     */
    val pointsIconResId: Int = 0
) {
    companion object {
        /**
         * 默认样式
         */
        val Default = TagAppearance()

        /**
         * 圆角样式
         */
        val Round = TagAppearance(
            cornerRadius = 12.dp,
            shape = RoundedCornerShape(12.dp)
        )

        /**
         * 胶囊样式（完全圆角）
         */
        val Capsule = TagAppearance(
            cornerRadius = 50.dp,
            shape = RoundedCornerShape(50.dp),
            horizontalPadding = 8.dp
        )

        /**
         * 方形样式（无圆角）
         */
        val Square = TagAppearance(
            cornerRadius = 0.dp,
            shape = RoundedCornerShape(0.dp)
        )
    }

    /**
     * 创建自定义圆角的样式
     */
    fun withCornerRadius(radius: Dp): TagAppearance {
        return copy(
            cornerRadius = radius,
            shape = RoundedCornerShape(radius)
        )
    }

    /**
     * 创建自定义文字大小的样式
     */
    fun withTextSize(size: TextUnit): TagAppearance {
        return copy(textSize = size)
    }

    /**
     * 创建自定义内边距的样式
     */
    fun withPadding(horizontal: Dp, vertical: Dp): TagAppearance {
        return copy(
            horizontalPadding = horizontal,
            verticalPadding = vertical
        )
    }
}
