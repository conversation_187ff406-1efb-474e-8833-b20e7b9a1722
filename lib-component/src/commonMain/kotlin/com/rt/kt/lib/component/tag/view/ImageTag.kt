package com.rt.kt.lib.component.tag.view

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import com.rt.kt.lib.component.tag.ImageLoaderManager
import com.rt.kt.lib.component.tag.TagBean
import com.rt.kt.lib.component.tag.TagType
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.platform.LocalDensity
import com.rt.kt.lib.component.tag.TagUtils

/**
 * @ClassName: ImageTag
 * @Description:图片标签组件
 * 支持网络图片和本地图片，对应原生库的FORM_IMAGE类型标签
 * @Date: 2025/6/4
 */

@Composable
fun ImageTag(
    tagBean: TagBean,
    onClick: ((TagBean) -> Unit)? = null,
    modifier: Modifier = Modifier,
    onImageSizeReady: ((Float, Float) -> Unit)? = null
) {
    require(tagBean.type == TagType.IMAGE) {
        "ImageTag only supports IMAGE type"
    }

    val appearance = tagBean.appearance

    // 点击修饰符
    val clickModifier = if (tagBean.isClickable && onClick != null) {
        Modifier.clickable { onClick(tagBean) }
    } else {
        Modifier
    }

    Row(
        modifier = modifier
            .then(clickModifier)
            .clip(appearance.shape),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        // 使用状态管理图片加载
        var imageBitmap by remember { mutableStateOf<ImageBitmap?>(null) }
        var isLoading by remember { mutableStateOf(true) }
        var hasError by remember { mutableStateOf(false) }

        val density = LocalDensity.current
        val tagHeight = TagUtils.calculateTagHeight(tagBean)
        val targetHeightDp = with(density) { tagHeight.toPx().toDp().value }

        // 通过回调机制加载图片
        LaunchedEffect(tagBean.imageUrl) {
            tagBean.imageUrl?.let { url ->
                isLoading = true
                hasError = false

                ImageLoaderManager.loadImageCompose(
                    url = url,
                    onSuccess = { imgBit ->
                        imageBitmap = imgBit
                        isLoading = false
                        hasError = false
                        // 图片加载成功后回调宽高（dp）
                        if (imgBit != null && imgBit.width > 0 && imgBit.height > 0) {
                            val scaledWidthDp = TagUtils.calculateImageTagRealWidth(imgBit,density, tagHeight)
                            val spacingDP = TagUtils.calculateTagSpacing(tagBean, tagBean.appearance)
                            onImageSizeReady?.invoke(scaledWidthDp+spacingDP, targetHeightDp)
                        }
                    },
                    onFailure = { failImg ->
                        imageBitmap = failImg
                        isLoading = false
                        hasError = true
                    }
                )
            } ?: run {
                // 没有URL，直接设置为失败状态
                isLoading = false
                hasError = true
            }
        }

        val finalWidth = TagUtils.calculateImageTagRealWidth(imageBitmap, density, tagHeight)
        Box(
            modifier = Modifier
                .size(width = finalWidth.dp, height = targetHeightDp.dp)
                .clip(appearance.shape),
            contentAlignment = Alignment.Center
        ) {
            when {
                isLoading -> {
                    // 加载中状态 - 显示空白
                    Box(modifier = Modifier.fillMaxSize())
                }
                imageBitmap != null -> {
                    // 加载成功 - 显示图片
                    Image(
                        bitmap = imageBitmap!!,
                        contentDescription = tagBean.text.ifBlank { "Tag image" },
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.Crop
                    )
                }
                else -> {
                    // 加载失败 - 显示空白
                    Box(modifier = Modifier.fillMaxSize())
                }
            }
        }
    }
}

/**
 * 默认加载中内容
 */
@Composable
fun DefaultLoadingContent() {
    CircularProgressIndicator(
        modifier = Modifier.size(16.dp),
        strokeWidth = 2.dp,
        color = MaterialTheme.colorScheme.primary
    )
}

/**
 * 默认错误内容
 */
@Composable
fun DefaultErrorContent() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .clip(RoundedCornerShape(4.dp)),
        contentAlignment = Alignment.Center
    ) {
//        Icon(
//            imageVector = Icons.Default.BrokenImage,
//            contentDescription = "Image load error",
//            tint = Color.Gray,
//            modifier = Modifier.size(16.dp)
//        )
    }
}
