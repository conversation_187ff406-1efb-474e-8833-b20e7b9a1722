/*
 * Copyright (c) 2024. 康成投资（中国）有限公司
 * http://www.rt-mart.com
 * 版权归本公司所有，不得私自使用、拷贝、修改、删除，否则视为侵权
 */

package lib.component.tag;

import android.graphics.Color;
import android.text.TextPaint;
import android.text.style.ClickableSpan;
import android.view.View;

import androidx.annotation.NonNull;

import lib.core.utils.ExCommonUtil;


/**
 * @author: jie.zhou7
 * @date: 2024/6/14
 * @information: 自定义点击span
 */
public class TagClickSpan extends ClickableSpan {
    private final TagBean data;
    private final OnTagClickListener mListener;

    public TagClickSpan(TagBean bean, OnTagClickListener listener) {
        this.data = bean;
        this.mListener = listener;
    }

    @Override
    public void onClick(@NonNull View widget) {
        if (!ExCommonUtil.isEmpty(this.mListener)) {
            this.mListener.onTagClick(data);
        }
    }



    @Override
    public void updateDrawState(@NonNull TextPaint ds) {
        ds.setColor(Color.TRANSPARENT);
        ds.setUnderlineText(false); // 移除下划线
    }
}
