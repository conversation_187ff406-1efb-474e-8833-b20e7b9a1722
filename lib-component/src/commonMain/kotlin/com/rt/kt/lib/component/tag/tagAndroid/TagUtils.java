/*
 * Copyright (c) 2024. 康成投资（中国）有限公司
 * http://www.rt-mart.com
 * 版权归本公司所有，不得私自使用、拷贝、修改、删除，否则视为侵权
 */

package lib.component.tag;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.Paint;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.SingleLineTransformationMethod;
import android.util.TypedValue;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.DrawableRes;
import androidx.collection.ArrayMap;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import lib.component.R;

/**
 * TagUtils
 * 打标工具类
 */
public class TagUtils {

    //标签类型-填充背景
    public static final int FORM_FILL = 1;
    //标签类型-图片标签
    public static final int FORM_IMAGE = 2;
    //标签类型-镂空背景
    public static final int FORM_STROKE = 3;
    //标签类型-折省标签
    public static final int FORM_ZS = 4;
    //标签类型-积分标
    public static final int FROM_JF = 5;
    //标签类型-描边背景
    public static final int FORM_FILL_AND_STROKE = -1;

    //宽度缓存集合
    private static final Map<Float, Float> SINGLE_MAP = new ArrayMap<>();
    //网络图片加载器
    private static INetPicLoader netPicLoader;

    //默认打标样式
    static TagAppearance defaultAppearance = TagAppearance.DEFAULT;
    //默认半圆角打标样式
    static TagAppearance roundAppearance = TagAppearance.ROUND;

    private static boolean isDebug = false;

    private static Bitmap jfBitmap;

    /**
     * 设置网络图片加载器
     *
     * @param context Context
     * @param loader  IRequestStrategy
     */
    public static void initNetLoader(Context context, INetPicLoader loader) {
        initNetLoader(context, loader, false);
    }

    public static void initNetLoader(Context context, INetPicLoader loader, boolean debug) {
        netPicLoader = loader;
        isDebug = debug;
    }

    public static void setDefaultAppearance(TagAppearance appearance) {
        defaultAppearance = appearance;
    }

    public static void setDefaultRoundAppearance(TagAppearance appearance) {
        roundAppearance = appearance;
    }

    public static void showRoundEnd(Context context, TextView textView, List<TagBean> tags, String content) {
        showTag(context, textView, tags, content, content.length(), roundAppearance, -1, false, null);
    }

    public static void showRoundEnd(Context context, TextView textView, List<TagBean> tags, String content, OnTagClickListener listener) {
        showTag(context, textView, tags, content, content.length(), roundAppearance, -1, false, listener);
    }

    public static void showRoundStart(Context context, TextView textView, List<TagBean> tags, String content) {
        showTag(context, textView, tags, content, 0, roundAppearance, -1, false, null);
    }

    public static void showRoundStart(Context context, TextView textView, List<TagBean> tags, String content, OnTagClickListener listener) {
        showTag(context, textView, tags, content, 0, roundAppearance, -1, false, listener);
    }

    public static void showRectEnd(Context context, TextView textView, List<TagBean> tags, String content) {
        showTag(context, textView, tags, content, content.length(), defaultAppearance, -1, false, null);
    }

    public static void showRectEnd(Context context, TextView textView, List<TagBean> tags, String content, OnTagClickListener listener) {
        showTag(context, textView, tags, content, content.length(), defaultAppearance, -1, false, listener);
    }

    public static void showRectStart(Context context, TextView textView, List<TagBean> tags, String content) {
        showTag(context, textView, tags, content, 0, defaultAppearance, -1, false, null);
    }

    public static void showRectStart(Context context, TextView textView, List<TagBean> tags, String content, OnTagClickListener listener) {
        showTag(context, textView, tags, content, 0, defaultAppearance, -1, false, listener);
    }

    public static void showRoundEnd(Context context, TextView textView, List<TagBean> tags, String content, int textSizeDp, boolean forceTagHeight) {
        showTag(context, textView, tags, content, content.length(), roundAppearance, textSizeDp, forceTagHeight, null);
    }

    public static void showRoundEnd(Context context, TextView textView, List<TagBean> tags, String content, int textSizeDp, boolean forceTagHeight, OnTagClickListener listener) {
        showTag(context, textView, tags, content, content.length(), roundAppearance, textSizeDp, forceTagHeight, listener);
    }

    public static void showRoundStart(Context context, TextView textView, List<TagBean> tags, String content, int textSizeDp, boolean forceTagHeight) {
        showTag(context, textView, tags, content, 0, roundAppearance, textSizeDp, forceTagHeight, null);
    }

    public static void showRoundStart(Context context, TextView textView, List<TagBean> tags, String content, int textSizeDp, boolean forceTagHeight, OnTagClickListener listener) {
        showTag(context, textView, tags, content, 0, roundAppearance, textSizeDp, forceTagHeight, listener);
    }

    public static void showRectEnd(Context context, TextView textView, List<TagBean> tags, String content, int textSizeDp, boolean forceTagHeight) {
        showTag(context, textView, tags, content, content.length(), defaultAppearance, textSizeDp, forceTagHeight, null);
    }

    public static void showRectEnd(Context context, TextView textView, List<TagBean> tags, String content, int textSizeDp, boolean forceTagHeight, OnTagClickListener listener) {
        showTag(context, textView, tags, content, content.length(), defaultAppearance, textSizeDp, forceTagHeight, listener);
    }

    public static void showRectStart(Context context, TextView textView, List<TagBean> tags, String content, int textSizeDp, boolean forceTagHeight) {
        showTag(context, textView, tags, content, 0, defaultAppearance, textSizeDp, forceTagHeight, null);
    }

    public static void showRectStart(Context context, TextView textView, List<TagBean> tags, String content, int textSizeDp, boolean forceTagHeight, OnTagClickListener listener) {
        showTag(context, textView, tags, content, 0, defaultAppearance, textSizeDp, forceTagHeight, listener);
    }

    public static void showTag(final Context c, final TextView textView, List<TagBean> tags, String text, int startPos, TagAppearance appearance) {
        showTag(c, textView, tags, text, startPos, appearance, -1, false, null);
    }

    public static void showTag(final Context c, final TextView textView, List<TagBean> tags, String text, int startPos, TagAppearance appearance, OnTagClickListener listener) {
        showTag(c, textView, tags, text, startPos, appearance, -1, false, listener);
    }

    /**
     * 打标
     *
     * @param c          Context
     * @param textView   TextView
     * @param tags       标签列表
     * @param text       标签后文字
     * @param startPos   起始位置 标签在前为0,标签在后为文本长度
     * @param appearance 未指定样式时的默认值
     */
    public static void showTag(final Context c, final TextView textView, List<TagBean> tags, String text, int startPos, TagAppearance appearance, int textSizeDp, boolean forceTagHeight, OnTagClickListener listener) {
        boolean hasClick = false;

        if (isDebug) {
            boolean isSingleLine = textView.getTransformationMethod() instanceof SingleLineTransformationMethod;
            if (isSingleLine) {
                String msg = c.getString(R.string.please_use_setMaxLines_instead_of_setSingleLine);
                Toast.makeText(c, msg, Toast.LENGTH_LONG).show();
                throw new RuntimeException(c.getString(R.string.please_use_setMaxLines_instead_of_setSingleLine));
            }
        }

        //设置textView最初始的值
        if (textSizeDp > 0) {
            textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, textSizeDp);
            textView.setTag(textView.getTextSize());
        } else {
            float textSize;
            if (textView.getTag() != null) {
                textSize = (float) textView.getTag();
                textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize);
            } else {
                textSize = textView.getTextSize();
                textView.setTag(textSize);
            }
        }

        if (text == null) text = "";
        if (tags == null || tags.isEmpty()) {
            setTextWithClearTag(textView, text);
            return;
        }
        int start = startPos;
        StringBuilder sb = new StringBuilder();
        //标签之后是否有文本内容
        boolean hasText = !TextUtils.isEmpty(text);
        //是否从头显示
        boolean fromStart = startPos == 0;
        int tagCount = tags.size();
        TagBean bean;
        for (int i = 0; i < tagCount; i++) {
            bean = tags.get(i);
            if (bean == null) continue;
            //如果标签无自定义样式,则使用默认值
            if (bean.appearance == null) {
                bean.appearance = appearance;
            }
            if (bean.form == FORM_FILL || bean.form == FORM_STROKE || bean.form == FORM_ZS || bean.form == FORM_FILL_AND_STROKE || bean.form == FROM_JF) {
                sb.append(bean.tagName);
            } else if (bean.form == FORM_IMAGE) {
                //空格占位
                sb.append(" ");
            }
            bean.setProperties(start, startPos + sb.length(), i, tagCount, fromStart, hasText);
            start = startPos + sb.length();
        }
        final SpannableString spanResult = new SpannableString(startPos == 0 ? sb + text : text + sb);
        for (final TagBean t : tags) {
            if (t.form == FORM_FILL) {
                spanResult.setSpan(new FillBgSpan(c, t), t.start, t.end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            } else if (t.form == FORM_IMAGE) {
                if (netPicLoader == null) continue;
                // 设置图片加载标记
                textView.setTag(R.id.tag_utils, t.picUrl + spanResult);
                netPicLoader.loadImage(t.picUrl, new ImageCallback() {
                    @Override
                    public void onBitmapReady(Bitmap bitmap) {
                        if (bitmap == null) return;
                        spanResult.setSpan(new TagImageSpan(c, bitmap, t), t.start, t.end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                        // 延时加载时判断后再加载
                        poseSetImgText(textView, spanResult, t.picUrl);
                    }

                    @Override
                    public void onFail(Bitmap failBitmap) {
                        if (failBitmap == null) return;
                        spanResult.setSpan(new TagImageSpan(c, failBitmap, t), t.start, t.end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                        // 延时加载时判断后再加载
                        poseSetImgText(textView, spanResult, t.picUrl);
                    }
                });
            } else if (t.form == FORM_STROKE) {
                spanResult.setSpan(new StrokeBgSpan(c, t), t.start, t.end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            } else if (t.form == FORM_FILL_AND_STROKE) {
                spanResult.setSpan(new FillAndStrokeBgSpan(c, t), t.start, t.end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            } else if (t.form == FORM_ZS) {
                spanResult.setSpan(new ZSBgTag(c, t), t.start, t.end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            } else if (t.form == FROM_JF) {
                spanResult.setSpan(new JFSpan(c, t), t.start, t.end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
            if (t.isClick) {
                hasClick = true;
                spanResult.setSpan(new TagClickSpan(t, listener), t.start, t.end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
        }

        float currentTextSize = textView.getPaint().getTextSize();

        //1.外部文字高度大于设置的固定标签高度 则显示标签的固定高度
        //2.外部文字高度小于设置的固定标签高度 则标签高度随文字高度自适应
        //3.若强制设置标签高度,需要调整外部文字大小兼容处理
        if (appearance.tagHeightDp > 0) {
            if ((forceTagHeight && needAdjustTxtSize(c, appearance.tagHeightDp, currentTextSize))) {
                //记录最初始的文字大小
                adjustTextViewSize(c, textView, currentTextSize, appearance.tagHeightDp, appearance.lineSpacingExtraDp);
                if (!TextUtils.isEmpty(text)) {
                    int begin = startPos == 0 ? sb.length() : 0;
                    int end = startPos == 0 ? (text + sb).length() : text.length();
                    for (int i = begin; i < end; i++) {
                        spanResult.setSpan(new VerticalCenterSpan(currentTextSize), i, i + 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                    }
                }
                for (TagBean tag : tags) {
                    tag.useFixedTagHeight = true;
                }
            } else if (!needAdjustTxtSize(c, appearance.tagHeightDp, currentTextSize)) {
                for (TagBean tag : tags) {
                    tag.useFixedTagHeight = true;
                }
            }
        }
        if (hasClick) {
            textView.setOnTouchListener(new ClickableSpanTouchListener());
            textView.setHighlightColor(Color.TRANSPARENT);
        }
        textView.setText(spanResult);
    }

    /**
     * 图片获取到之后判断后赋值
     * <p>
     * 场景：列表页加载第二页时有一些图片标，图片刚开始加载就【刷新全部列表】。此时列表的holder被打乱复用。之前加载图片标的组件被设置了新值。此时图片回来了把刚赋值新值的组件文本改为了最初延时加载的错误文本
     * 解决：延迟加载后数据加一个判断条件，条件验证过之后再重新赋值
     *
     * @param textView 组件
     * @param text     设置的文本
     * @param picUrl   加载的图片标
     */
    private static void poseSetImgText(TextView textView, CharSequence text, String picUrl) {
        textView.post(() -> {
            String urlTag = (String) textView.getTag(R.id.tag_utils);
            if (Objects.equals(urlTag, picUrl + text)) {
                setTextWithClearTag(textView, text);
            }
        });
    }

    /**
     * 为了解决加载图片时post的延时runnable回来后已失效，所以加一个Tag判断数据有效性
     *
     * @param textView textView
     * @param text     text
     */
    private static void setTextWithClearTag(TextView textView, CharSequence text) {
        textView.setText(text);
        // 移除Tag
        textView.setTag(R.id.tag_utils, "");
    }

    /**
     * 获取文字高度
     * 缓存6个大小
     *
     * @param tagPaint 标签文字paint
     * @return 高度值
     */
    static float getTextHeight(Paint tagPaint) {
        float textSize = tagPaint.getTextSize();
        if (SINGLE_MAP.containsKey(textSize)) {
            Float size = SINGLE_MAP.get(textSize);
            if (size != null && size > 0F) {
                return size;
            }
        }
        Paint.FontMetrics fm = tagPaint.getFontMetrics();
        float singleSize = fm.descent - fm.ascent;
        if (SINGLE_MAP.size() < 6) {
            SINGLE_MAP.put(textSize, singleSize);
        }
        return singleSize;
    }

    /**
     * 确定文字绘制y坐标上的baseline调整值
     *
     * @param y     原始基线坐标
     * @param rawFm 外部paint的fm
     * @param tagFm 标签文字的fm
     * @return 调整后的值
     */
    static float adjustBaseLine(int y, Paint.FontMetrics rawFm, Paint.FontMetrics tagFm) {
        float offset1 = (rawFm.descent - rawFm.ascent) / 2F - rawFm.descent;
        float offset2 = -(tagFm.ascent + tagFm.descent) / 2F;
        return y - offset1 + offset2;
    }

    /**
     * 处理行高
     * 1 如果标签后没有文字,则再6.0以上机器高度为0,需要设置参数fm内部的值,使用paint的FontMetricsInt赋值
     * 2 如果多标签中有图片标签且在第一位,标签后又无文字,图片设置的行高会影响整个行高,图片过小时会导致后面的标签显示不全,要特别处理
     *
     * @param paint Paint
     * @param fm    Paint.FontMetricsInt
     */
    static void checkLineFM(Paint paint, Paint.FontMetricsInt fm) {
        if (fm == null) return;
        Paint.FontMetrics textFM = paint.getFontMetrics();
        if (fm.top == 0 || (fm.bottom - fm.top) < (textFM.bottom - textFM.top)) {
            paint.getFontMetricsInt(fm);
        }
    }

    /**
     * 测量文字宽度
     *
     * @param paint Paint
     * @param text  字符串
     * @param start 起始索引
     * @param end   结束索引
     * @return 宽度
     */
    static float measureText(Paint paint, CharSequence text, int start, int end) {
        if ((start | end | (end - start) | (text.length() - end)) < 0) {
            return 0;
        }
        return paint.measureText(text, start, end);
    }

    //屏幕密度换算
    private static float density = -1F;

    /**
     * dp转px
     *
     * @param c   Context
     * @param dip dp
     * @return px
     */
    static int dpToPx(Context c, float dip) {
        if (density == -1F) {
            density = c.getResources().getDisplayMetrics().density;
        }
        return (int) (dip * density + 0.5F);
    }

    /**
     * 调整文字大小
     */
    private static void adjustTextViewSize(Context context, TextView tv, float originTextSize, float tagHeightDp, float lineSpacingExtraDp) {
        float textSize = originTextSize;
        //若标签高度大于后面文字的高度则需要调整
        while (needAdjustTxtSize(context, tagHeightDp, textSize)) {
            textSize += 1;
        }
        tv.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize);
    }

    /**
     * 判断是否需要调整外部文字大小
     *
     * @param context
     * @param tagHeightDp
     * @param textSize
     * @return
     */
    private static boolean needAdjustTxtSize(Context context, float tagHeightDp, float textSize) {
        float textViewHeightPx = getTextHeight(new TextPaint(), textSize);
        int tagHeightPx = dpToPx(context, tagHeightDp);
        return tagHeightPx > textViewHeightPx;
    }

    /**
     * 获取文字的高度
     *
     * @param paint    画笔
     * @param textSize 文字size
     * @return 文字的高度
     */
    private static float getTextHeight(TextPaint paint, float textSize) {
        paint.setTextSize(textSize);
        Paint.FontMetrics fontMetrics = paint.getFontMetrics();
        return fontMetrics.descent - fontMetrics.ascent;
    }

    public static Bitmap getJFBitmap(Resources resources, @DrawableRes int resId) {
        if (jfBitmap != null) {
            return jfBitmap;
        }
        jfBitmap = BitmapFactory.decodeResource(resources, resId);
        return jfBitmap;
    }
}
