/*
 * Copyright (c) 2024. 康成投资（中国）有限公司
 * http://www.rt-mart.com
 * 版权归本公司所有，不得私自使用、拷贝、修改、删除，否则视为侵权
 */

package lib.component.tag;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.text.style.ReplacementSpan;

import androidx.annotation.NonNull;

import lib.component.R;


/**
 * @author: jie.zhou7
 * @date: 2024/6/13
 * @information: 带右键的span
 */
public abstract class ReplacementSpanWithArrow extends ReplacementSpan {
    private Drawable arrowDrawable;
    float arrowWidth = 0;   //箭头宽度
    float arrowHeight = 0;   //箭头高度
    float arrowMargin = 0;  //箭头距离文字的边距

    /**
     * 初始化箭头
     * @param context
     * @param bean
     */
    protected void StrokeArrowSpan(Context context, TagBean bean){
        arrowDrawable = context.getResources().getDrawable(R.drawable.icon_arrow_right);
        arrowWidth = TagUtils.dpToPx(context, 5);
        arrowHeight = TagUtils.dpToPx(context, 7);
        arrowMargin = TagUtils.dpToPx(context, 6);
        // 设置箭头颜色
        arrowDrawable.mutate(); // 防止与其他引用该资源的Drawable共享状态
        arrowDrawable.setColorFilter(TagAppearance.safeParseColor(bean.textColor, Color.BLACK), PorterDuff.Mode.SRC_IN);
    }

    /**
     * 绘制箭头
     * @param canvas
     * @param rect
     */
    protected void drawArrow(@NonNull Canvas canvas, @NonNull RectF rect){
        // 绘制文字之后，计算并绘制箭头图标
        float arrowX = rect.right - arrowWidth / 2 - arrowMargin; // 箭头位于边框右侧居中
        float arrowY = rect.centerY() - arrowHeight / 2; // 箭头垂直居中于文本高度

        // 绘制箭头图标
        arrowDrawable.setBounds((int)arrowX, (int)arrowY, (int)(arrowX + arrowWidth), (int)(arrowY + arrowHeight));
        arrowDrawable.draw(canvas);
    }
}
