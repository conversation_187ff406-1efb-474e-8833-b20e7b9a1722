package com.rt.kt.lib.component.tag

import androidx.compose.ui.graphics.Color

/**
 * @ClassName: TagBean
 * @Description:
 * @Date: 2025/6/4
 */
/**
 * 标签数据模型
 *
 * 这是KMP Tag Library的核心数据类，用于定义标签的所有属性和行为。
 * 对应原生Android版本的TagBean类，完全适配Compose使用。
 *
 * 支持的标签类型：
 * - FILL: 填充背景标签（纯色或渐变）
 * - STROKE: 镂空边框标签（只有边框，透明背景）
 * - IMAGE: 图片标签（使用网络或本地图片）
 * - DISCOUNT: 折省标签（特殊的分段显示标签）
 * - POINTS: 积分标签（带图标的积分标签）
 * - FILL_AND_STROKE: 填充+描边标签（既有背景又有边框）
 *
 * 使用示例：
 * ```kotlin
 * val tag = TagBean(
 *     type = TagType.FILL,
 *     text = "新品",
 *     textColor = Color.White,
 *     backgroundColor = Color.Red,
 *     appearance = TagAppearance.Round
 * )
 * ```
 */
data class TagBean(
    /**
     * 标签类型
     *
     * 决定标签的渲染方式和外观：
     * - FILL: 填充背景标签，支持纯色和渐变
     * - STROKE: 镂空边框标签，只显示边框
     * - IMAGE: 图片标签，显示网络或本地图片
     * - DISCOUNT: 折省标签，分段显示不同内容
     * - POINTS: 积分标签，左侧图标+右侧文字
     * - FILL_AND_STROKE: 填充+描边，既有背景又有边框
     */
    val type: TagType = TagType.FILL,

    /**
     * 标签文字内容
     *
     * 对于不同类型的标签：
     * - FILL/STROKE/FILL_AND_STROKE: 显示的文字内容
     * - DISCOUNT: 会被分割显示（如"折省"分为"折"和"省"）
     * - POINTS: 积分数值文字（如"100积分"）
     * - IMAGE: 图片的alt文字（可选）
     */
    val text: String = "",

    /**
     * 文字颜色
     *
     * 标签内文字的颜色，支持所有Compose Color格式。
     * 建议与背景色形成足够的对比度以确保可读性。
     */
    val textColor: Color = Color.Black,

    /**
     * 背景颜色
     *
     * 标签的主背景色：
     * - FILL类型：填充整个标签背景
     * - DISCOUNT类型：第一部分的背景色
     * - POINTS类型：图标部分的背景色
     * - STROKE类型：不使用此属性
     */
    val backgroundColor: Color = Color.White,

    /**
     * 背景结束颜色（用于渐变）
     *
     * 当设置此属性时，标签将显示从backgroundColor到backgroundEndColor的水平渐变。
     * 对于DISCOUNT和POINTS类型，此颜色用于第二部分的背景。
     * 设置为null时显示纯色背景。
     */
    val backgroundEndColor: Color? = null,

    /**
     * 边框颜色
     *
     * 标签边框的颜色：
     * - STROKE类型：边框颜色
     * - FILL_AND_STROKE类型：描边颜色
     * - 其他类型：可选的装饰边框颜色
     */
    val borderColor: Color = Color.Black,

    /**
     * 图片URL（用于图片标签）
     *
     * 仅在type为IMAGE或POINTS时使用：
     * - IMAGE类型：标签显示的图片URL
     * - POINTS类型：积分图标的URL
     * 支持网络URL和本地资源路径
     */
    val imageUrl: String? = null,

    /**
     * 是否可点击
     *
     * 设置为true时：
     * - 标签右侧会显示箭头图标
     * - 支持点击交互
     * - 可以触发onTagClick回调
     */
    val isClickable: Boolean = false,

    /**
     * 点击提示文字
     *
     * 当标签被点击时可以显示的提示信息，
     * 通常用于Toast提示。
     * 仅在isClickable为true时有效。
     */
    val clickToast: String? = null,

    /**
     * 标签样式配置
     *
     * 控制标签的外观样式，包括：
     * - 文字大小、字体粗细
     * - 圆角大小、边框宽度
     * - 内边距、外边距
     * - 其他视觉属性
     *
     * 可以使用预定义样式或自定义样式。
     */
    val appearance: TagAppearance = TagAppearance.Default,

    /**
     * 是否使用固定标签高度
     *
     * 设置为true时：
     * - 使用appearance.tagHeight作为固定高度
     * - 使用appearance.fixedTextSize作为文字大小
     * - 忽略文字内容对高度的影响
     */
    val useFixedHeight: Boolean = false,

    /**
     * 标签索引（在多标签中的位置）
     *
     * 在标签组合中的位置索引，从0开始。
     * 用于确定标签间距和特殊样式处理。
     * 通常由TagUtils.processTagList自动设置。
     */
    val tagIndex: Int = 0,

    /**
     * 标签总数
     *
     * 当前标签组合中的标签总数。
     * 用于确定是否为第一个或最后一个标签。
     * 通常由TagUtils.processTagList自动设置。
     */
    val tagCount: Int = 1,

    /**
     * 标签显示位置，true-在文字前面，false-在文字后面
     */
    val showAtStart: Boolean = true
) {

    // ==================== 计算辅助属性 ====================

    /**
     * 是否从开始位置显示（用于间距计算）
     */
    val fromStart: Boolean get() = showAtStart

    /**
     * 是否有文字内容（用于间距计算）
     */
    val hasText: Boolean get() = text.isNotBlank()

    // ==================== 标签状态方法 ====================
    /**
     * 是否为第一个标签
     */
    fun isFirstTag(): Boolean = tagIndex == 0

    /**
     * 是否为最后一个标签
     */
    fun isLastTag(): Boolean = tagIndex == tagCount - 1

    /**
     * 是否为渐变背景
     */
    fun hasGradientBackground(): Boolean = backgroundEndColor != null

    /**
     * 获取处理后的背景结束颜色
     * 处理与COLOR_NONE的潜在冲突
     */
    fun getProcessedBackgroundEndColor(): Color? {
        return backgroundEndColor?.let { endColor ->
            TagUtils.processColorForCompatibility(endColor)
        }
    }

    /**
     * 检查标签数据是否有效
     */
    fun isValid(): Boolean {
        return when (type) {
            TagType.IMAGE -> !imageUrl.isNullOrBlank()
            else -> text.isNotBlank()
        }
    }

    /**
     * 获取标签的显示文字
     * 对于不同类型的标签返回合适的显示文字
     */
    fun getDisplayText(): String {
        return when (type) {
            TagType.IMAGE -> imageUrl ?: ""
            else -> text
        }
    }

    /**
     * 检查是否需要加载图片
     */
    fun needsImageLoading(): Boolean {
        return (type == TagType.IMAGE || type == TagType.POINTS) && !imageUrl.isNullOrBlank()
    }

    /**
     * 获取标签的唯一标识
     * 用于缓存和去重
     */
    fun getUniqueId(): String {
        return "${type.name}_${text}_${imageUrl ?: ""}_${tagIndex}"
    }

    /**
     * 创建标签的副本，但更新索引信息
     */
    fun copyWithIndex(index: Int, totalCount: Int): TagBean {
        return copy(tagIndex = index, tagCount = totalCount)
    }

    /**
     * 检查标签是否应该显示箭头
     */
    fun shouldShowArrow(): Boolean = isClickable

    /**
     * 获取标签的对比文字颜色
     * 根据背景色自动计算合适的文字颜色
     */
    fun getContrastTextColor(): Color {
        val luminance = 0.299 * backgroundColor.red + 0.587 * backgroundColor.green + 0.114 * backgroundColor.blue
        return if (luminance < 0.5) Color.White else Color.Black
    }
}
/**
 * 标签类型枚举
 */
enum class TagType {
    /**
     * 填充背景标签 - 纯色或渐变背景
     */
    FILL,

    /**
     * 图片标签 - 使用网络或本地图片
     */
    IMAGE,

    /**
     * 镂空边框标签 - 只有边框，透明背景
     */
    STROKE,

    /**
     * 折省标签 - 特殊样式的标签
     */
    DISCOUNT,

    /**
     * 积分标签 - 积分相关的特殊标签
     */
    POINTS,

    /**
     * 填充+描边标签 - 既有背景又有边框
     */
    FILL_AND_STROKE
}

