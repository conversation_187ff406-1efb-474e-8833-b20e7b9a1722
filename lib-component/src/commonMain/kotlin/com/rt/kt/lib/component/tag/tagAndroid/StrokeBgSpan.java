/*
 * Copyright (c) 2024. 康成投资（中国）有限公司
 * http://www.rt-mart.com
 * 版权归本公司所有，不得私自使用、拷贝、修改、删除，否则视为侵权
 */

package lib.component.tag;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.text.style.ReplacementSpan;

import androidx.annotation.NonNull;

import lib.core.utils.ExCommonUtil;

/**
 * StrokeBgSpan
 * <p>
 * 镂空背景标签
 * <p>
 * Modified by ye.xue 0on 2021/03/05
 */
public class StrokeBgSpan extends ReplacementSpanWithArrow {

    private final Context context;
    private final TagBean data;

    private final Paint framePaint;
    private final Paint tagPaint;
    private final RectF frameRectF;

    private float frameWidth;
    private float frameHeight;

    private final float textMargin;
    private final float tagMargin;

    /**
     * 构造方法
     *
     * @param c    上下文
     * @param bean TagBean
     */
    public StrokeBgSpan(Context c, TagBean bean) {
        this.context = c.getApplicationContext();
        this.data = bean;
        //前面已经设置过,这里只是做下防护
        if (this.data.appearance == null) {
            this.data.appearance = TagAppearance.DEFAULT;
        }
        framePaint = new Paint();
        framePaint.setColor(TagAppearance.safeParseColor(data.borderColor, Color.BLACK));
        framePaint.setStyle(Paint.Style.STROKE);
        framePaint.setStrokeWidth(TagUtils.dpToPx(context, this.data.appearance.borderSizeDp));
        framePaint.setAntiAlias(true);
        framePaint.setDither(true);
        tagPaint = new Paint();
        tagPaint.setColor(TagAppearance.safeParseColor(data.textColor, Color.BLACK));
        tagPaint.setTextAlign(Paint.Align.CENTER);
        tagPaint.setAntiAlias(true);
        tagPaint.setDither(true);
        tagPaint.setFakeBoldText(data.appearance.tagTextBold);
        frameRectF = new RectF();

        textMargin = TagUtils.dpToPx(context, this.data.appearance.textMarginDp);
        tagMargin = TagUtils.dpToPx(context, this.data.appearance.tagMarginDp);
        if (bean.isClick) {
            this.StrokeArrowSpan(context, data);
        }
    }

    @SuppressWarnings("SuspiciousNameCombination")
    @Override
    public int getSize(@NonNull Paint paint, CharSequence text, int start, int end, Paint.FontMetricsInt fm) {
        TagUtils.checkLineFM(paint, fm);
        //设置标签内文本大小
        float realTagTextSize = data.appearance.getTagTextSize(context, paint,data.useFixedTagHeight);
        tagPaint.setTextSize(realTagTextSize);
        //标签文字与边框间距处理
        float paddingV = data.appearance.getTagTextPaddingV(context, realTagTextSize);
        float paddingH = data.appearance.getTagTextPaddingH(context, realTagTextSize);
        /*
         * 如果是单字,绘制范围设定为正方形
         * 否则按照标签文字实际宽度
         */
        if (end - start == 1) {
            frameWidth = data.appearance.dealForSquareFrame(tagPaint, paddingV);
            frameHeight = frameWidth;
        } else {
            frameWidth = TagUtils.measureText(tagPaint, text, start, end) + 2 * paddingH;
            Paint.FontMetrics tagFM = tagPaint.getFontMetrics();
            frameHeight = tagFM.descent - tagFM.ascent + 2 * paddingV;
        }
        //增加矩形宽度
        if (data.isClick) {
            frameWidth = frameWidth + arrowWidth + arrowMargin;
        }
        int spanWidth = Math.round(frameWidth);

        //若设置了tag的行高则忽律之前的默认设置
        if (data.appearance.tagHeightDp > 0 && data.useFixedTagHeight) {
            frameHeight = TagUtils.dpToPx(context, data.appearance.tagHeightDp);
        }

        //多个标签,添加标签间距
        if (!data.isLastTag()) {
            spanWidth += tagMargin;
        }
        //从开始位置显示,最后一个标签,并且后面有文字内容,添加标签与文字间距
        if (data.fromStart && data.hasText && data.isLastTag()) {
            spanWidth += textMargin;
        }

        //从结束位置显示,第一个标签,添加便签与文字间的间距
        if (!data.fromStart && data.isFirstTag()) {
            spanWidth += textMargin;
        }
        return spanWidth;
    }

    @Override
    public void draw(@NonNull Canvas canvas, CharSequence text, int start, int end, float x, int top, int y, int bottom, Paint paint) {
        //计算边框上下位置，以文字高度为基准
        Paint.FontMetrics rawFM = paint.getFontMetrics();
        //外部原始边框范围与实际边框差值
        float heightDiff = (rawFM.descent - rawFM.ascent - frameHeight) / 2F;
        /*
         *  注意点
         *  绘制矩形时,绘制中心点为边线的中间,计算时要校正
         *  坐标left,多tag时要添加margin
         */
        float frameLeft;
        /*if (x < 0.001F) {
            //认为是从头开始
            frameLeft = x;
        } else {
            frameLeft = data.isFirstTag() ? x : (x + tagMargin);
        }*/
        if (!data.fromStart && data.isFirstTag()) {
            frameLeft = x + textMargin;
        } else {
            frameLeft = x;
        }
        float frameTop = y + rawFM.ascent + heightDiff;
        //打标框的范围
        float borderSize = TagUtils.dpToPx(context, data.appearance.borderSizeDp);
        frameRectF.set(frameLeft + borderSize / 2F,
                       frameTop + borderSize / 2F,
                       frameLeft + frameWidth - borderSize / 2F,
                       frameTop + frameHeight - borderSize / 2F);
        //绘制箭头
        if (data.isClick) {
            this.drawArrow(canvas, frameRectF);
        }
        //边框圆角弧度
        float frameCorner = data.appearance.getCornerSize(context, frameHeight);
        //绘制边框
        canvas.drawRoundRect(frameRectF, frameCorner, frameCorner, framePaint);
        //绘制文字
        canvas.drawText(text, start, end,
                        (2F * frameLeft + frameWidth - arrowWidth - arrowMargin) / 2F,
                        TagUtils.adjustBaseLine(y, rawFM, tagPaint.getFontMetrics()),
                        tagPaint);
    }
}
