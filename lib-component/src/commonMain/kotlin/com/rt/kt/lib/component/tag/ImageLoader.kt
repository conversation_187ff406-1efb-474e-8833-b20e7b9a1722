package com.rt.kt.lib.component.tag

import androidx.compose.ui.graphics.ImageBitmap

/**
 * @ClassName: ImageLoader
 * @Description:图片加载接口
 * @Date: 2025/6/4
 */

/**
 * 图片加载回调接口
 */
interface ImageCallback {
    /**
     * 图片加载成功回调
     * @param imageBitmap 加载成功的ImageBitmap
     */
    fun onBitmapReady(imageBitmap: ImageBitmap?)

    /**
     * 图片加载失败回调
     * @param failPainter 失败时的Painter
     */
    fun onFail(failImageBitmap: ImageBitmap?)
}

/**
 * Compose图片加载器接口
 */
interface TagImageLoader {

    /**
     * 加载图片
     * @param url 图片URL
     * @param imageCallback 图片加载回调
     */
    fun loadImage(url: String, imageCallback: ImageCallback?)
}


/**
 * 图片加载管理器
 * 管理全局的图片加载器实例
 */
object ImageLoaderManager {

    /**
     * Compose图片加载器实例
     */
    private var tagImageLoader: TagImageLoader? = null

    /**
     * 调试模式标志
     */
    private var isDebugMode: Boolean = false

    /**
     * 初始化图片加载器
     * @param loader 图片加载器实例
     * @param debug 是否开启调试模式
     */
    fun initializeCompose(loader: TagImageLoader?, debug: Boolean = false) {
        tagImageLoader = loader
        isDebugMode = debug

        if (debug && loader == null) {
            println("⚠️ ImageLoaderManager: TagImageLoader is null")
        }
    }

    /**
     * 获取图片加载器
     */
    fun getTagImageLoader(): TagImageLoader? = tagImageLoader

    /**
     * 是否为调试模式
     */
    fun isDebug(): Boolean = isDebugMode

    /**
     * 检查是否已初始化
     */
    fun isInitialized(): Boolean = tagImageLoader != null

    /**
     * 在Compose中加载图片
     *
     * @param url 图片URL
     * @param onSuccess 成功回调
     * @param onFailure 失败回调
     */
    fun loadImageCompose(
        url: String,
        onSuccess: (ImageBitmap?) -> Unit,
        onFailure: (ImageBitmap?) -> Unit
    ) {
        if (url.isBlank()) {
            if (isDebugMode) {
                println("⚠️ ImageLoaderManager: Image URL is blank")
            }
            onFailure(null)
            return
        }

        val loader = tagImageLoader
        if (loader == null) {
            if (isDebugMode) {
                println("⚠️ ImageLoaderManager: TagImageLoader not initialized")
            }
            onFailure(null)
            return
        }

        try {
            loader.loadImage(url, object : ImageCallback {
                override fun onBitmapReady(imageBitmap: ImageBitmap?) {
                    if (isDebugMode) {
                        println("✅ ImageLoaderManager: Image loaded successfully: $url")
                    }
                    onSuccess(imageBitmap)
                }

                override fun onFail(failImageBitmap: ImageBitmap?) {
                    if (isDebugMode) {
                        println("❌ ImageLoaderManager: Failed to load image: $url")
                    }
                    onFailure(failImageBitmap)
                }
            })
        } catch (e: Exception) {
            if (isDebugMode) {
                println("⚠️ ImageLoaderManager: Error loading image: ${e.message}")
            }
            onFailure(null)
        }
    }

    /**
     * 重置管理器状态
     */
    fun reset() {
        tagImageLoader = null
        isDebugMode = false
    }
}

/**
 * 默认的空图片加载器实现
 */
class EmptyTagImageLoader : TagImageLoader {
    override fun loadImage(url: String, imageCallback: ImageCallback?) {
        //如果没有图片加载器，直接调用失败回调
        imageCallback?.onFail(null)
    }
}
